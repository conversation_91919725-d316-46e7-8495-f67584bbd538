import {
  pgTable,
  serial,
  varchar,
  text,
  timestamp,
  integer,
  boolean,
  date,
  time,
  decimal,
  json,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

export const users = pgTable('users', {
  id: serial('id').primary<PERSON>ey(),
  clerkId: varchar('clerk_id', { length: 255 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 100 }),
  companyName: varchar('company_name', { length: 255 }),
  role: varchar('role', { length: 20 }).notNull().default('member'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at'),
});

export const teams = pgTable('teams', {
  id: serial('id').primary<PERSON>ey(),
  name: varchar('name', { length: 100 }).notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  stripeCustomerId: text('stripe_customer_id').unique(),
  stripeSubscriptionId: text('stripe_subscription_id').unique(),
  stripeProductId: text('stripe_product_id'),
  planName: varchar('plan_name', { length: 50 }),
  subscriptionStatus: varchar('subscription_status', { length: 20 }),
});

export const teamMembers = pgTable('team_members', {
  id: serial('id').primaryKey(),
  userId: integer('user_id')
    .notNull()
    .references(() => users.id),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),
  role: varchar('role', { length: 50 }).notNull(),
  joinedAt: timestamp('joined_at').notNull().defaultNow(),
});

export const activityLogs = pgTable('activity_logs', {
  id: serial('id').primaryKey(),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),
  userId: integer('user_id').references(() => users.id),
  action: text('action').notNull(),
  timestamp: timestamp('timestamp').notNull().defaultNow(),
  ipAddress: varchar('ip_address', { length: 45 }),
});

export const invitations = pgTable('invitations', {
  id: serial('id').primaryKey(),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),
  email: varchar('email', { length: 255 }).notNull(),
  role: varchar('role', { length: 50 }).notNull(),
  invitedBy: integer('invited_by')
    .notNull()
    .references(() => users.id),
  invitedAt: timestamp('invited_at').notNull().defaultNow(),
  status: varchar('status', { length: 20 }).notNull().default('pending'),
});

export const teamsRelations = relations(teams, ({ many }) => ({
  teamMembers: many(teamMembers),
  activityLogs: many(activityLogs),
  invitations: many(invitations),
  cranes: many(cranes),
}));

export const usersRelations = relations(users, ({ many }) => ({
  teamMembers: many(teamMembers),
  invitationsSent: many(invitations),
  createdBookings: many(bookings, { relationName: 'createdBy' }),
}));

export const invitationsRelations = relations(invitations, ({ one }) => ({
  team: one(teams, {
    fields: [invitations.teamId],
    references: [teams.id],
  }),
  invitedBy: one(users, {
    fields: [invitations.invitedBy],
    references: [users.id],
  }),
}));

export const teamMembersRelations = relations(teamMembers, ({ one }) => ({
  user: one(users, {
    fields: [teamMembers.userId],
    references: [users.id],
  }),
  team: one(teams, {
    fields: [teamMembers.teamId],
    references: [teams.id],
  }),
}));

export const activityLogsRelations = relations(activityLogs, ({ one }) => ({
  team: one(teams, {
    fields: [activityLogs.teamId],
    references: [teams.id],
  }),
  user: one(users, {
    fields: [activityLogs.userId],
    references: [users.id],
  }),
}));

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Team = typeof teams.$inferSelect;
export type NewTeam = typeof teams.$inferInsert;
export type TeamMember = typeof teamMembers.$inferSelect;
export type NewTeamMember = typeof teamMembers.$inferInsert;
export type ActivityLog = typeof activityLogs.$inferSelect;
export type NewActivityLog = typeof activityLogs.$inferInsert;
export type Invitation = typeof invitations.$inferSelect;
export type NewInvitation = typeof invitations.$inferInsert;
export type TeamDataWithMembers = Team & {
  teamMembers: (TeamMember & {
    user: Pick<User, 'id' | 'name' | 'email'>;
  })[];
};

// Project and Crane Schema
export const organization = pgTable('organization', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const projects = pgTable('projects', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  organizationId: text('organization_id')
    .notNull()
    .references(() => organization.id),
  createdBy: text('created_by'),
  location: json('location'),
  safetyCompliance: json('safety_compliance'),
  startDate: date('start_date'),
  endDate: date('end_date'),
  status: varchar('status', { length: 20 }).default('active'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at'),
});

export const cranes = pgTable('cranes', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  model: varchar('model', { length: 100 }),
  type: varchar('type', { length: 50 }).notNull(),
  capacity: decimal('capacity', { precision: 10, scale: 2 }),
  height: decimal('height', { precision: 10, scale: 2 }),
  reach: decimal('reach', { precision: 10, scale: 2 }),
  teamId: integer('team_id')
    .notNull()
    .references(() => teams.id),

  status: varchar('status', { length: 20 }).notNull().default('available'),
  lastMaintenanceDate: date('last_maintenance_date'),
  nextMaintenanceDate: date('next_maintenance_date'),

  // TODO: Add these fields after migration
  // assignedProjectId: text('assigned_project_id').references(() => projects.id),
  // operatingHours: json('operating_hours'),
  // slotDuration: integer('slot_duration_minutes').notNull().default(30),
  // advanceBookingDays: integer('advance_booking_days').notNull().default(7),
  // siteLocation: varchar('site_location', { length: 100 }),

  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at'),
  metadata: json('metadata'),
});

export const bookings = pgTable('bookings', {
  id: serial('id').primaryKey(),
  craneId: integer('crane_id')
    .notNull()
    .references(() => cranes.id),
  projectId: text('project_id')
    .notNull()
    .references(() => projects.id),
  startDate: timestamp('start_date').notNull(),
  endDate: timestamp('end_date').notNull(),
  createdById: integer('created_by_id')
    .notNull()
    .references(() => users.id),
  status: varchar('status', { length: 20 }).notNull().default('scheduled'),
  notes: text('notes'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at'),
});

// TODO: Lift bookings table for crane lift scheduling (temporarily disabled)
/*
export const liftBookings = pgTable('lift_bookings', {
  id: serial('id').primaryKey(),
  craneId: integer('crane_id')
    .notNull()
    .references(() => cranes.id),
  projectId: text('project_id')
    .notNull()
    .references(() => projects.id),
  bookedById: integer('booked_by_id')
    .notNull()
    .references(() => users.id),

  // Lift scheduling details
  liftDate: date('lift_date').notNull(), // The day of the lift
  startTime: varchar('start_time', { length: 8 }).notNull(), // Start time (e.g., "10:30")
  numberOfLifts: integer('number_of_lifts').notNull().default(1), // How many 30-min slots
  slotDuration: integer('slot_duration_minutes').notNull().default(30), // Minutes per lift

  // Lift details
  liftPurpose: varchar('lift_purpose', { length: 50 }).notNull(), // 'materials', 'equipment', 'personnel', 'other'
  itemDescription: text('item_description').notNull(), // What's being lifted
  pickupLocation: varchar('pickup_location', { length: 100 }), // Where from (e.g., "Ground level", "Loading bay")
  dropoffLocation: varchar('dropoff_location', { length: 100 }).notNull(), // Where to (e.g., "Level 5", "Roof")
  estimatedWeight: decimal('estimated_weight', { precision: 10, scale: 2 }), // Weight in tonnes
  specialRequirements: text('special_requirements'), // Special handling, safety requirements

  // Booking management
  status: varchar('status', { length: 20 }).notNull().default('pending'), // 'pending', 'confirmed', 'in-progress', 'completed', 'cancelled'
  confirmedById: integer('confirmed_by_id').references(() => users.id), // Admin/operator who confirmed
  confirmedAt: timestamp('confirmed_at'),

  // Contact details
  contactPerson: varchar('contact_person', { length: 100 }).notNull(),
  contactPhone: varchar('contact_phone', { length: 20 }),

  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at'),
});
*/

// Relations for projects, cranes, and bookings
export const projectsRelations = relations(projects, ({ one, many }) => ({
  organization: one(organization, {
    fields: [projects.organizationId],
    references: [organization.id],
  }),
  bookings: many(bookings),
}));

export const cranesRelations = relations(cranes, ({ one, many }) => ({
  team: one(teams, {
    fields: [cranes.teamId],
    references: [teams.id],
  }),
  // TODO: Add after migration
  // assignedProject: one(projects, {
  //   fields: [cranes.assignedProjectId],
  //   references: [projects.id],
  // }),
  bookings: many(bookings),
  // liftBookings: many(liftBookings), // TODO: Enable after implementing lift bookings
}));

export const bookingsRelations = relations(bookings, ({ one }) => ({
  crane: one(cranes, {
    fields: [bookings.craneId],
    references: [cranes.id],
  }),
  project: one(projects, {
    fields: [bookings.projectId],
    references: [projects.id],
  }),
  createdBy: one(users, {
    fields: [bookings.createdById],
    references: [users.id],
  }),
}));

// TODO: Enable after implementing lift bookings
/*
export const liftBookingsRelations = relations(liftBookings, ({ one }) => ({
  crane: one(cranes, {
    fields: [liftBookings.craneId],
    references: [cranes.id],
  }),
  project: one(projects, {
    fields: [liftBookings.projectId],
    references: [projects.id],
  }),
  bookedBy: one(users, {
    fields: [liftBookings.bookedById],
    references: [users.id],
  }),
  confirmedBy: one(users, {
    fields: [liftBookings.confirmedById],
    references: [users.id],
  }),
}));
*/

// Organization relations
export const organizationRelations = relations(organization, ({ many }) => ({
  projects: many(projects),
}));

// Types for projects, cranes, and bookings
export type Project = typeof projects.$inferSelect;
export type NewProject = typeof projects.$inferInsert;
export type Crane = typeof cranes.$inferSelect;
export type NewCrane = typeof cranes.$inferInsert;
export type Booking = typeof bookings.$inferSelect;
export type NewBooking = typeof bookings.$inferInsert;
// TODO: Enable after implementing lift bookings
// export type LiftBooking = typeof liftBookings.$inferSelect;
// export type NewLiftBooking = typeof liftBookings.$inferInsert;

export enum ActivityType {
  SIGN_UP = 'SIGN_UP',
  SIGN_IN = 'SIGN_IN',
  SIGN_OUT = 'SIGN_OUT',
  UPDATE_PASSWORD = 'UPDATE_PASSWORD',
  DELETE_ACCOUNT = 'DELETE_ACCOUNT',
  UPDATE_ACCOUNT = 'UPDATE_ACCOUNT',
  CREATE_TEAM = 'CREATE_TEAM',
  REMOVE_TEAM_MEMBER = 'REMOVE_TEAM_MEMBER',
  INVITE_TEAM_MEMBER = 'INVITE_TEAM_MEMBER',
  ACCEPT_INVITATION = 'ACCEPT_INVITATION',
  CREATE_PROJECT = 'CREATE_PROJECT',
  UPDATE_PROJECT = 'UPDATE_PROJECT',
  DELETE_PROJECT = 'DELETE_PROJECT',
  CREATE_CRANE = 'CREATE_CRANE',
  UPDATE_CRANE = 'UPDATE_CRANE',
  DELETE_CRANE = 'DELETE_CRANE',
  CREATE_BOOKING = 'CREATE_BOOKING',
  UPDATE_BOOKING = 'UPDATE_BOOKING',
  DELETE_BOOKING = 'DELETE_BOOKING',
}
