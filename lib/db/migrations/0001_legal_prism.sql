CREATE TABLE "bookings" (
	"id" serial PRIMARY KEY NOT NULL,
	"crane_id" integer NOT NULL,
	"project_id" text NOT NULL,
	"start_date" timestamp NOT NULL,
	"end_date" timestamp NOT NULL,
	"created_by_id" integer NOT NULL,
	"status" varchar(20) DEFAULT 'scheduled' NOT NULL,
	"notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "cranes" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"model" varchar(100),
	"type" varchar(50) NOT NULL,
	"capacity" numeric(10, 2),
	"height" numeric(10, 2),
	"reach" numeric(10, 2),
	"team_id" integer NOT NULL,
	"assigned_project_id" text,
	"operating_hours" json,
	"slot_duration_minutes" integer DEFAULT 30 NOT NULL,
	"advance_booking_days" integer DEFAULT 7 NOT NULL,
	"status" varchar(20) DEFAULT 'available' NOT NULL,
	"last_maintenance_date" date,
	"next_maintenance_date" date,
	"site_location" varchar(100),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp,
	"metadata" json
);
--> statement-breakpoint
CREATE TABLE "lift_bookings" (
	"id" serial PRIMARY KEY NOT NULL,
	"crane_id" integer NOT NULL,
	"project_id" text NOT NULL,
	"booked_by_id" integer NOT NULL,
	"lift_date" date NOT NULL,
	"start_time" time NOT NULL,
	"number_of_lifts" integer DEFAULT 1 NOT NULL,
	"slot_duration_minutes" integer DEFAULT 30 NOT NULL,
	"lift_purpose" varchar(50) NOT NULL,
	"item_description" text NOT NULL,
	"pickup_location" varchar(100),
	"dropoff_location" varchar(100) NOT NULL,
	"estimated_weight" numeric(10, 2),
	"special_requirements" text,
	"status" varchar(20) DEFAULT 'pending' NOT NULL,
	"confirmed_by_id" integer,
	"confirmed_at" timestamp,
	"contact_person" varchar(100) NOT NULL,
	"contact_phone" varchar(20),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "organization" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "projects" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"organization_id" text NOT NULL,
	"created_by" text,
	"location" json,
	"safety_compliance" json,
	"start_date" date,
	"end_date" date,
	"status" varchar(20) DEFAULT 'active',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "clerk_id" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "company_name" varchar(255);--> statement-breakpoint
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_crane_id_cranes_id_fk" FOREIGN KEY ("crane_id") REFERENCES "public"."cranes"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_project_id_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_created_by_id_users_id_fk" FOREIGN KEY ("created_by_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cranes" ADD CONSTRAINT "cranes_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cranes" ADD CONSTRAINT "cranes_assigned_project_id_projects_id_fk" FOREIGN KEY ("assigned_project_id") REFERENCES "public"."projects"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lift_bookings" ADD CONSTRAINT "lift_bookings_crane_id_cranes_id_fk" FOREIGN KEY ("crane_id") REFERENCES "public"."cranes"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lift_bookings" ADD CONSTRAINT "lift_bookings_project_id_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lift_bookings" ADD CONSTRAINT "lift_bookings_booked_by_id_users_id_fk" FOREIGN KEY ("booked_by_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lift_bookings" ADD CONSTRAINT "lift_bookings_confirmed_by_id_users_id_fk" FOREIGN KEY ("confirmed_by_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "projects" ADD CONSTRAINT "projects_organization_id_organization_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organization"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "users" DROP COLUMN "password_hash";--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "users_clerk_id_unique" UNIQUE("clerk_id");