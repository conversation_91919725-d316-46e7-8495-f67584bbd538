-- Migration to fix bookings table project_id column type
-- This migration converts project_id from integer to text to match the projects table

-- First, check if bookings table exists and what type project_id currently is
DO $$
DECLARE
    column_type text;
BEGIN
    -- Check if bookings table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'bookings') THEN
        -- Get the current data type of project_id column
        SELECT data_type INTO column_type
        FROM information_schema.columns 
        WHERE table_name = 'bookings' 
        AND column_name = 'project_id';
        
        RAISE NOTICE 'Current project_id column type: %', column_type;
        
        -- If project_id is currently integer, we need to convert it
        IF column_type = 'integer' THEN
            RAISE NOTICE 'Converting project_id from integer to text...';
            
            -- Step 1: Drop foreign key constraint if it exists
            IF EXISTS (
                SELECT 1 FROM information_schema.table_constraints 
                WHERE constraint_name = 'bookings_project_id_projects_id_fk'
            ) THEN
                ALTER TABLE bookings DROP CONSTRAINT bookings_project_id_projects_id_fk;
                RAISE NOTICE 'Dropped foreign key constraint';
            END IF;
            
            -- Step 2: Add a temporary column with text type
            ALTER TABLE bookings ADD COLUMN project_id_temp text;
            RAISE NOTICE 'Added temporary text column';
            
            -- Step 3: Copy data from integer column to text column
            -- For now, we'll just set all existing bookings to null since we don't have valid text project IDs
            -- In a real migration, you'd need to map integer IDs to text IDs
            UPDATE bookings SET project_id_temp = NULL;
            RAISE NOTICE 'Copied data to temporary column (set to NULL for safety)';
            
            -- Step 4: Drop the old integer column
            ALTER TABLE bookings DROP COLUMN project_id;
            RAISE NOTICE 'Dropped old integer column';
            
            -- Step 5: Rename the temporary column
            ALTER TABLE bookings RENAME COLUMN project_id_temp TO project_id;
            RAISE NOTICE 'Renamed temporary column';
            
            -- Step 6: Add NOT NULL constraint (after we have proper data)
            -- For now, we'll leave it nullable until we have proper project IDs
            -- ALTER TABLE bookings ALTER COLUMN project_id SET NOT NULL;
            
            -- Step 7: Add foreign key constraint back (commented out until we have proper data)
            -- ALTER TABLE bookings 
            -- ADD CONSTRAINT bookings_project_id_projects_id_fk 
            -- FOREIGN KEY (project_id) REFERENCES projects(id);
            
            RAISE NOTICE 'Migration completed successfully';
        ELSE
            RAISE NOTICE 'project_id column is already text type, no conversion needed';
        END IF;
    ELSE
        RAISE NOTICE 'bookings table does not exist, creating it with correct schema';
        
        -- Create bookings table with correct schema
        CREATE TABLE bookings (
            id serial PRIMARY KEY NOT NULL,
            crane_id integer NOT NULL,
            project_id text NOT NULL,
            start_date timestamp NOT NULL,
            end_date timestamp NOT NULL,
            created_by_id integer NOT NULL,
            status varchar(20) DEFAULT 'scheduled' NOT NULL,
            notes text,
            created_at timestamp DEFAULT now() NOT NULL,
            updated_at timestamp DEFAULT now() NOT NULL,
            deleted_at timestamp
        );
        
        -- Add foreign key constraints
        ALTER TABLE bookings 
        ADD CONSTRAINT bookings_crane_id_cranes_id_fk 
        FOREIGN KEY (crane_id) REFERENCES cranes(id);
        
        ALTER TABLE bookings 
        ADD CONSTRAINT bookings_project_id_projects_id_fk 
        FOREIGN KEY (project_id) REFERENCES projects(id);
        
        ALTER TABLE bookings 
        ADD CONSTRAINT bookings_created_by_id_users_id_fk 
        FOREIGN KEY (created_by_id) REFERENCES users(id);
        
        RAISE NOTICE 'Created bookings table with correct schema';
    END IF;
END $$;
--> statement-breakpoint

-- Ensure projects table has text ID (should already be correct)
DO $$
BEGIN
    -- Check if projects table exists and has text ID
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'projects') THEN
        -- Check if projects.id is text type
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'projects' 
            AND column_name = 'id' 
            AND data_type = 'text'
        ) THEN
            RAISE NOTICE 'projects.id is already text type';
        ELSE
            RAISE NOTICE 'WARNING: projects.id is not text type, this may cause issues';
        END IF;
    ELSE
        RAISE NOTICE 'WARNING: projects table does not exist';
    END IF;
END $$;
