{"id": "f7af6fb2-f018-4a0a-be56-7b48cbadd0b7", "prevId": "261fd993-fb2c-43e7-89d6-cd58786c5f58", "version": "7", "dialect": "postgresql", "tables": {"public.activity_logs": {"name": "activity_logs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"activity_logs_team_id_teams_id_fk": {"name": "activity_logs_team_id_teams_id_fk", "tableFrom": "activity_logs", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "activity_logs_user_id_users_id_fk": {"name": "activity_logs_user_id_users_id_fk", "tableFrom": "activity_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bookings": {"name": "bookings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "crane_id": {"name": "crane_id", "type": "integer", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_by_id": {"name": "created_by_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"bookings_crane_id_cranes_id_fk": {"name": "bookings_crane_id_cranes_id_fk", "tableFrom": "bookings", "tableTo": "cranes", "columnsFrom": ["crane_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "bookings_project_id_projects_id_fk": {"name": "bookings_project_id_projects_id_fk", "tableFrom": "bookings", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "bookings_created_by_id_users_id_fk": {"name": "bookings_created_by_id_users_id_fk", "tableFrom": "bookings", "tableTo": "users", "columnsFrom": ["created_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cranes": {"name": "cranes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "capacity": {"name": "capacity", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "reach": {"name": "reach", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": true}, "assigned_project_id": {"name": "assigned_project_id", "type": "text", "primaryKey": false, "notNull": false}, "operating_hours": {"name": "operating_hours", "type": "json", "primaryKey": false, "notNull": false}, "slot_duration_minutes": {"name": "slot_duration_minutes", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "advance_booking_days": {"name": "advance_booking_days", "type": "integer", "primaryKey": false, "notNull": true, "default": 7}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'available'"}, "last_maintenance_date": {"name": "last_maintenance_date", "type": "date", "primaryKey": false, "notNull": false}, "next_maintenance_date": {"name": "next_maintenance_date", "type": "date", "primaryKey": false, "notNull": false}, "site_location": {"name": "site_location", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"cranes_team_id_teams_id_fk": {"name": "cranes_team_id_teams_id_fk", "tableFrom": "cranes", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "cranes_assigned_project_id_projects_id_fk": {"name": "cranes_assigned_project_id_projects_id_fk", "tableFrom": "cranes", "tableTo": "projects", "columnsFrom": ["assigned_project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "invited_by": {"name": "invited_by", "type": "integer", "primaryKey": false, "notNull": true}, "invited_at": {"name": "invited_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}}, "indexes": {}, "foreignKeys": {"invitations_team_id_teams_id_fk": {"name": "invitations_team_id_teams_id_fk", "tableFrom": "invitations", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invitations_invited_by_users_id_fk": {"name": "invitations_invited_by_users_id_fk", "tableFrom": "invitations", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.lift_bookings": {"name": "lift_bookings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "crane_id": {"name": "crane_id", "type": "integer", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "text", "primaryKey": false, "notNull": true}, "booked_by_id": {"name": "booked_by_id", "type": "integer", "primaryKey": false, "notNull": true}, "lift_date": {"name": "lift_date", "type": "date", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "time", "primaryKey": false, "notNull": true}, "number_of_lifts": {"name": "number_of_lifts", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "slot_duration_minutes": {"name": "slot_duration_minutes", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "lift_purpose": {"name": "lift_purpose", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "item_description": {"name": "item_description", "type": "text", "primaryKey": false, "notNull": true}, "pickup_location": {"name": "pickup_location", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "dropoff_location": {"name": "dropoff_location", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "estimated_weight": {"name": "estimated_weight", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "special_requirements": {"name": "special_requirements", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "confirmed_by_id": {"name": "confirmed_by_id", "type": "integer", "primaryKey": false, "notNull": false}, "confirmed_at": {"name": "confirmed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "contact_person": {"name": "contact_person", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "contact_phone": {"name": "contact_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"lift_bookings_crane_id_cranes_id_fk": {"name": "lift_bookings_crane_id_cranes_id_fk", "tableFrom": "lift_bookings", "tableTo": "cranes", "columnsFrom": ["crane_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "lift_bookings_project_id_projects_id_fk": {"name": "lift_bookings_project_id_projects_id_fk", "tableFrom": "lift_bookings", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "lift_bookings_booked_by_id_users_id_fk": {"name": "lift_bookings_booked_by_id_users_id_fk", "tableFrom": "lift_bookings", "tableTo": "users", "columnsFrom": ["booked_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "lift_bookings_confirmed_by_id_users_id_fk": {"name": "lift_bookings_confirmed_by_id_users_id_fk", "tableFrom": "lift_bookings", "tableTo": "users", "columnsFrom": ["confirmed_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization": {"name": "organization", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "json", "primaryKey": false, "notNull": false}, "safety_compliance": {"name": "safety_compliance", "type": "json", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"projects_organization_id_organization_id_fk": {"name": "projects_organization_id_organization_id_fk", "tableFrom": "projects", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_members": {"name": "team_members", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "integer", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"team_members_user_id_users_id_fk": {"name": "team_members_user_id_users_id_fk", "tableFrom": "team_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "team_members_team_id_teams_id_fk": {"name": "team_members_team_id_teams_id_fk", "tableFrom": "team_members", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teams": {"name": "teams", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "stripe_product_id": {"name": "stripe_product_id", "type": "text", "primaryKey": false, "notNull": false}, "plan_name": {"name": "plan_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "subscription_status": {"name": "subscription_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"teams_stripe_customer_id_unique": {"name": "teams_stripe_customer_id_unique", "nullsNotDistinct": false, "columns": ["stripe_customer_id"]}, "teams_stripe_subscription_id_unique": {"name": "teams_stripe_subscription_id_unique", "nullsNotDistinct": false, "columns": ["stripe_subscription_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "clerk_id": {"name": "clerk_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'member'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_clerk_id_unique": {"name": "users_clerk_id_unique", "nullsNotDistinct": false, "columns": ["clerk_id"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}