-- Manual migration to add lift booking functionality

-- Add new columns to cranes table for lift scheduling
ALTER TABLE cranes 
ADD COLUMN IF NOT EXISTS assigned_project_id TEXT REFERENCES projects(id),
ADD COLUMN IF NOT EXISTS operating_hours JSON,
ADD COLUMN IF NOT EXISTS slot_duration_minutes INTEGER DEFAULT 30,
ADD COLUMN IF NOT EXISTS advance_booking_days INTEGER DEFAULT 7,
ADD COLUMN IF NOT EXISTS site_location VARCHAR(100);

-- Create lift_bookings table
CREATE TABLE IF NOT EXISTS lift_bookings (
  id SERIAL PRIMARY KEY,
  crane_id INTEGER NOT NULL REFERENCES cranes(id),
  project_id TEXT NOT NULL REFERENCES projects(id),
  booked_by_id INTEGER NOT NULL REFERENCES users(id),
  
  -- Lift scheduling details
  lift_date DATE NOT NULL,
  start_time TIME NOT NULL,
  number_of_lifts INTEGER NOT NULL DEFAULT 1,
  slot_duration_minutes INTEGER NOT NULL DEFAULT 30,
  
  -- Lift details
  lift_purpose VARCHAR(50) NOT NULL,
  item_description TEXT NOT NULL,
  pickup_location VARCHAR(100),
  dropoff_location VARCHAR(100) NOT NULL,
  estimated_weight DECIMAL(10,2),
  special_requirements TEXT,
  
  -- Booking management
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  confirmed_by_id INTEGER REFERENCES users(id),
  confirmed_at TIMESTAMP,
  
  -- Contact details
  contact_person VARCHAR(100) NOT NULL,
  contact_phone VARCHAR(20),
  
  -- Timestamps
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_lift_bookings_crane_date ON lift_bookings(crane_id, lift_date);
CREATE INDEX IF NOT EXISTS idx_lift_bookings_project ON lift_bookings(project_id);
CREATE INDEX IF NOT EXISTS idx_lift_bookings_status ON lift_bookings(status);
CREATE INDEX IF NOT EXISTS idx_lift_bookings_booked_by ON lift_bookings(booked_by_id);

-- Add some sample operating hours for existing cranes
UPDATE cranes SET operating_hours = '{
  "monday": {"start": "07:00", "end": "17:00"},
  "tuesday": {"start": "07:00", "end": "17:00"},
  "wednesday": {"start": "07:00", "end": "17:00"},
  "thursday": {"start": "07:00", "end": "17:00"},
  "friday": {"start": "07:00", "end": "17:00"},
  "saturday": {"start": "08:00", "end": "12:00"},
  "sunday": {"start": null, "end": null}
}'::json WHERE operating_hours IS NULL;

-- Add clerk_id column to users if it doesn't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS clerk_id VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS company_name VARCHAR(255);

-- Add unique constraint on clerk_id if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'users_clerk_id_unique' 
        AND table_name = 'users'
    ) THEN
        ALTER TABLE users ADD CONSTRAINT users_clerk_id_unique UNIQUE(clerk_id);
    END IF;
END $$;
