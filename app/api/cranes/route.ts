import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { cranes, teams, teamMembers, users, activityLogs, ActivityType } from "@/lib/db/schema";
import { eq, and, isNull } from "drizzle-orm";

// GET /api/cranes - Get all cranes for the current user's team
export async function GET(req: NextRequest) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for cranes API");

    const userCranes = await db.query.cranes.findMany({
      where: isNull(cranes.deletedAt),
      with: {
        team: true,
      },
      orderBy: (crane) => [crane.name],
    });

    return NextResponse.json({ cranes: userCranes });
  } catch (error) {
    console.error("[CRANES_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/cranes - Create a new crane
export async function POST(req: NextRequest) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for cranes POST API");

    const body = await req.json();
    console.log("📝 Received crane data:", body);

    const {
      name,
      model,
      type,
      capacity,
      height,
      reach,
      teamId,
      status,
      lastMaintenanceDate,
      nextMaintenanceDate
    } = body;

    // Validation
    if (!name || !name.trim()) {
      return new NextResponse("Name is required", { status: 400 });
    }

    if (!type) {
      return new NextResponse("Type is required", { status: 400 });
    }

    if (!teamId) {
      return new NextResponse("Team ID is required", { status: 400 });
    }

    // Validate that the team exists
    const team = await db.query.teams.findFirst({
      where: eq(teams.id, teamId),
    });

    if (!team) {
      return new NextResponse("Team not found", { status: 404 });
    }

    // Prepare crane data for insertion
    const craneData = {
      name: name.trim(),
      model: model?.trim() || null,
      type,
      capacity: capacity ? String(capacity) : null,
      height: height ? String(height) : null,
      reach: reach ? String(reach) : null,
      teamId,
      status: status || "available",
      lastMaintenanceDate: lastMaintenanceDate ? lastMaintenanceDate : null,
      nextMaintenanceDate: nextMaintenanceDate ? nextMaintenanceDate : null,
    };

    console.log("💾 Inserting crane data:", craneData);

    // Create the crane using Drizzle's insert method
    const [newCrane] = await db.insert(cranes).values(craneData).returning();

    console.log("✅ Created crane:", newCrane);

    // Log the activity (temporarily bypass user lookup for testing)
    try {
      await db.insert(activityLogs).values({
        teamId: teamId,
        userId: null, // Will be set properly when auth is restored
        action: ActivityType.CREATE_CRANE,
        ipAddress: req.headers.get("x-forwarded-for") || undefined,
      });
    } catch (logError) {
      console.warn("⚠️ Failed to log activity:", logError);
      // Don't fail the request if logging fails
    }

    return NextResponse.json({ crane: newCrane });
  } catch (error) {
    console.error("[CRANES_POST] Error creating crane:", error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('foreign key constraint')) {
        return new NextResponse("Invalid team ID", { status: 400 });
      }
      if (error.message.includes('duplicate key')) {
        return new NextResponse("A crane with this name already exists", { status: 409 });
      }
    }

    return new NextResponse("Internal error", { status: 500 });
  }
}
