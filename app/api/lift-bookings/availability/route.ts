import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/lib/db';
import { liftBookings, cranes } from '@/lib/db/schema';
import { eq, and, isNull } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { craneId, liftDate, numberOfLifts = 1 } = body;

    if (!craneId || !liftDate) {
      return NextResponse.json(
        { error: 'Missing required fields: craneId and liftDate' },
        { status: 400 }
      );
    }

    // Get crane details including operating hours
    const crane = await db.query.cranes.findFirst({
      where: eq(cranes.id, parseInt(craneId)),
    });

    if (!crane) {
      return NextResponse.json({ error: 'Crane not found' }, { status: 404 });
    }

    // Get the day of the week for the lift date
    const date = new Date(liftDate);
    const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'lowercase' });

    // Get operating hours for the day
    const operatingHours = crane.operatingHours as any;
    const dayHours = operatingHours?.[dayOfWeek];

    if (!dayHours || !dayHours.start || !dayHours.end) {
      return NextResponse.json({
        availableSlots: [],
        message: 'Crane is not operating on this day'
      });
    }

    // Get existing bookings for the crane on this date
    const existingBookings = await db.query.liftBookings.findMany({
      where: and(
        eq(liftBookings.craneId, parseInt(craneId)),
        eq(liftBookings.liftDate, liftDate),
        isNull(liftBookings.deletedAt),
        // Include all active booking statuses
        // TODO: Add proper status filtering
      ),
    });

    // Generate all possible time slots for the day
    const slotDuration = crane.slotDuration || 30; // Default 30 minutes
    const availableSlots = generateAvailableSlots(
      dayHours.start,
      dayHours.end,
      slotDuration,
      numberOfLifts,
      existingBookings
    );

    return NextResponse.json({
      availableSlots,
      operatingHours: dayHours,
      slotDuration,
      totalSlots: availableSlots.length
    });

  } catch (error) {
    console.error('Error checking availability:', error);
    return NextResponse.json(
      { error: 'Failed to check availability' },
      { status: 500 }
    );
  }
}

function generateAvailableSlots(
  startTime: string,
  endTime: string,
  slotDuration: number,
  numberOfLifts: number,
  existingBookings: any[]
): Array<{ startTime: string; endTime: string; available: boolean }> {
  const slots = [];
  
  // Convert start and end times to minutes
  const [startHours, startMinutes] = startTime.split(':').map(Number);
  const [endHours, endMinutes] = endTime.split(':').map(Number);
  
  const startTotalMinutes = startHours * 60 + startMinutes;
  const endTotalMinutes = endHours * 60 + endMinutes;
  
  // Calculate total duration needed for the booking
  const totalDurationNeeded = numberOfLifts * slotDuration;
  
  // Generate slots
  for (let currentMinutes = startTotalMinutes; 
       currentMinutes + totalDurationNeeded <= endTotalMinutes; 
       currentMinutes += slotDuration) {
    
    const slotStartTime = formatTime(currentMinutes);
    const slotEndTime = formatTime(currentMinutes + totalDurationNeeded);
    
    // Check if this slot conflicts with existing bookings
    const isAvailable = !hasConflict(
      currentMinutes,
      currentMinutes + totalDurationNeeded,
      existingBookings,
      slotDuration
    );
    
    slots.push({
      startTime: slotStartTime,
      endTime: slotEndTime,
      available: isAvailable
    });
  }
  
  return slots;
}

function hasConflict(
  slotStart: number,
  slotEnd: number,
  existingBookings: any[],
  slotDuration: number
): boolean {
  for (const booking of existingBookings) {
    // Skip cancelled bookings
    if (booking.status === 'cancelled') continue;
    
    const [bookingHours, bookingMinutes] = booking.startTime.split(':').map(Number);
    const bookingStart = bookingHours * 60 + bookingMinutes;
    const bookingEnd = bookingStart + (booking.numberOfLifts * (booking.slotDuration || slotDuration));
    
    // Check for overlap
    if (slotStart < bookingEnd && slotEnd > bookingStart) {
      return true;
    }
  }
  
  return false;
}

function formatTime(totalMinutes: number): string {
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}
