import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/lib/db';
import { liftBookings, cranes, projects, users } from '@/lib/db/schema';
import { eq, and, gte, lte, isNull, desc, sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 [TEMP] Bypassing auth for lift bookings API');

    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const craneId = searchParams.get('craneId');
    const projectId = searchParams.get('projectId');
    const status = searchParams.get('status');

    // Build WHERE conditions
    let whereConditions = [isNull(liftBookings.deletedAt)];

    // Filter by date if provided
    if (date) {
      whereConditions.push(eq(liftBookings.liftDate, date));
    }

    // Filter by crane if provided
    if (craneId) {
      whereConditions.push(eq(liftBookings.craneId, parseInt(craneId)));
    }

    // Filter by project if provided
    if (projectId) {
      whereConditions.push(eq(liftBookings.projectId, projectId));
    }

    // Filter by status if provided
    if (status && status !== 'all') {
      whereConditions.push(eq(liftBookings.status, status));
    }

    const userLiftBookings = await db.query.liftBookings.findMany({
      where: and(...whereConditions),
      with: {
        crane: {
          columns: {
            id: true,
            name: true,
            type: true,
            siteLocation: true,
          },
        },
        project: {
          columns: {
            id: true,
            name: true,
          },
        },
        bookedBy: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
        confirmedBy: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: [desc(liftBookings.liftDate), liftBookings.startTime],
    });

    // Transform the data to include calculated fields
    const transformedBookings = userLiftBookings.map(booking => {
      const startTime = booking.startTime;
      const endTime = calculateEndTime(startTime, booking.numberOfLifts, booking.slotDuration);
      
      return {
        ...booking,
        craneName: booking.crane?.name || 'Unknown Crane',
        projectName: booking.project?.name || 'Unknown Project',
        endTime,
        totalDuration: booking.numberOfLifts * booking.slotDuration,
      };
    });

    return NextResponse.json({ liftBookings: transformedBookings });
  } catch (error) {
    console.error('Error fetching lift bookings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch lift bookings' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 [TEMP] Bypassing auth for lift bookings API');
    // const { userId } = await auth();
    // if (!userId) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    const body = await request.json();
    const {
      craneId,
      projectId,
      liftDate,
      startTime,
      numberOfLifts,
      liftPurpose,
      itemDescription,
      pickupLocation,
      dropoffLocation,
      estimatedWeight,
      specialRequirements,
      contactPerson,
      contactPhone,
    } = body;

    // Validate required fields
    if (!craneId || !projectId || !liftDate || !startTime || !numberOfLifts || 
        !liftPurpose || !itemDescription || !dropoffLocation || !contactPerson) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get user ID from database (temporarily use first user)
    const user = await db.query.users.findFirst();

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if the time slots are available
    const isAvailable = await checkTimeSlotAvailability(
      craneId,
      liftDate,
      startTime,
      numberOfLifts
    );

    if (!isAvailable) {
      return NextResponse.json(
        { error: 'Selected time slots are not available' },
        { status: 409 }
      );
    }

    // Create the lift booking
    const [newLiftBooking] = await db
      .insert(liftBookings)
      .values({
        craneId: parseInt(craneId),
        projectId,
        bookedById: user.id,
        liftDate,
        startTime,
        numberOfLifts: parseInt(numberOfLifts),
        liftPurpose,
        itemDescription,
        pickupLocation,
        dropoffLocation,
        estimatedWeight: estimatedWeight ? parseFloat(estimatedWeight) : null,
        specialRequirements,
        contactPerson,
        contactPhone,
        status: 'pending',
      })
      .returning();

    return NextResponse.json({ liftBooking: newLiftBooking }, { status: 201 });
  } catch (error) {
    console.error('Error creating lift booking:', error);
    return NextResponse.json(
      { error: 'Failed to create lift booking' },
      { status: 500 }
    );
  }
}

// Helper function to calculate end time
function calculateEndTime(startTime: string, numberOfLifts: number, slotDuration: number): string {
  const [hours, minutes] = startTime.split(':').map(Number);
  const totalMinutes = hours * 60 + minutes + (numberOfLifts * slotDuration);
  const endHours = Math.floor(totalMinutes / 60);
  const endMinutes = totalMinutes % 60;
  
  return `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;
}

// Helper function to check time slot availability
async function checkTimeSlotAvailability(
  craneId: number,
  liftDate: string,
  startTime: string,
  numberOfLifts: number
): Promise<boolean> {
  try {
    // Get existing bookings for the same crane and date
    const existingBookings = await db.query.liftBookings.findMany({
      where: and(
        eq(liftBookings.craneId, craneId),
        eq(liftBookings.liftDate, liftDate),
        isNull(liftBookings.deletedAt),
        // Only check confirmed and pending bookings
        eq(liftBookings.status, 'pending') // We'll expand this to include other active statuses
      ),
    });

    // Calculate the time range for the new booking
    const [startHours, startMinutes] = startTime.split(':').map(Number);
    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = startTotalMinutes + (numberOfLifts * 30); // Assuming 30-minute slots

    // Check for conflicts with existing bookings
    for (const booking of existingBookings) {
      const [bookingStartHours, bookingStartMinutes] = booking.startTime.split(':').map(Number);
      const bookingStartTotalMinutes = bookingStartHours * 60 + bookingStartMinutes;
      const bookingEndTotalMinutes = bookingStartTotalMinutes + (booking.numberOfLifts * booking.slotDuration);

      // Check if there's an overlap
      if (
        (startTotalMinutes < bookingEndTotalMinutes && endTotalMinutes > bookingStartTotalMinutes)
      ) {
        return false; // Conflict found
      }
    }

    return true; // No conflicts
  } catch (error) {
    console.error('Error checking availability:', error);
    return false;
  }
}
