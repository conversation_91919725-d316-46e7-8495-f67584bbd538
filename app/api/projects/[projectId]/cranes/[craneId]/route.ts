import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { projects, cranes, bookings, users } from "@/lib/db/schema";
import { eq, and, isNull } from "drizzle-orm";

// DELETE /api/projects/[projectId]/cranes/[craneId] - Remove crane assignment from project
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string; craneId: string }> }
) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { projectId, craneId: craneIdStr } = await params;
    const craneId = parseInt(craneIdStr);

    if (isNaN(craneId)) {
      return new NextResponse("Invalid crane ID", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Verify the project exists
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // Verify the crane exists
    const crane = await db.query.cranes.findFirst({
      where: and(eq(cranes.id, craneId), isNull(cranes.deletedAt)),
    });

    if (!crane) {
      return new NextResponse("Crane not found", { status: 404 });
    }

    // Find active bookings for this crane and project
    const activeBookings = await db.query.bookings.findMany({
      where: and(
        eq(bookings.craneId, craneId),
        eq(bookings.projectId, projectId),
        isNull(bookings.deletedAt)
      ),
    });

    if (activeBookings.length === 0) {
      return new NextResponse("No active bookings found for this crane and project", { status: 404 });
    }

    // Soft delete all bookings for this crane and project
    await db
      .update(bookings)
      .set({
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(bookings.craneId, craneId),
          eq(bookings.projectId, projectId),
          isNull(bookings.deletedAt)
        )
      );

    return NextResponse.json({ 
      message: "Crane assignment removed successfully",
      removedBookings: activeBookings.length 
    });
  } catch (error) {
    console.error("[PROJECT_CRANE_DELETE]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// GET /api/projects/[projectId]/cranes/[craneId] - Get specific crane assignment details
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string; craneId: string }> }
) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for project crane detail GET API");

    const { projectId, craneId: craneIdStr } = await params;
    const craneId = parseInt(craneIdStr);

    if (isNaN(craneId)) {
      return new NextResponse("Invalid crane ID", { status: 400 });
    }

    // Verify the project exists
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // Get crane details with bookings for this project
    const crane = await db.query.cranes.findFirst({
      where: and(eq(cranes.id, craneId), isNull(cranes.deletedAt)),
      with: {
        bookings: {
          where: and(
            eq(bookings.projectId, projectId),
            isNull(bookings.deletedAt)
          ),
        },
      },
    });

    if (!crane) {
      return new NextResponse("Crane not found", { status: 404 });
    }

    return NextResponse.json({ crane });
  } catch (error) {
    console.error("[PROJECT_CRANE_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
