import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { projects, cranes, bookings, users } from "@/lib/db/schema";
import { eq, and, isNull, sql } from "drizzle-orm";

// GET /api/projects/[projectId]/cranes - Get cranes assigned to a project
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for project cranes GET API");

    const { projectId } = await params;

    // Verify the project exists
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // Get bookings for this project with crane details
    const projectBookings = await db.query.bookings.findMany({
      where: and(
        eq(bookings.projectId, projectId),
        isNull(bookings.deletedAt)
      ),
      with: {
        crane: true,
      },
    });

    // Group bookings by crane and calculate stats
    const craneMap = new Map();

    projectBookings.forEach(booking => {
      const crane = booking.crane;
      if (!crane) return;

      if (!craneMap.has(crane.id)) {
        craneMap.set(crane.id, {
          ...crane,
          booking_count: 0,
          next_booking_start: null,
          last_booking_end: null,
        });
      }

      const craneData = craneMap.get(crane.id);
      craneData.booking_count += 1;

      const startDate = new Date(booking.startDate);
      const endDate = new Date(booking.endDate);

      if (!craneData.next_booking_start || startDate < new Date(craneData.next_booking_start)) {
        craneData.next_booking_start = booking.startDate;
      }

      if (!craneData.last_booking_end || endDate > new Date(craneData.last_booking_end)) {
        craneData.last_booking_end = booking.endDate;
      }
    });

    const projectCranes = Array.from(craneMap.values()).sort((a, b) => a.name.localeCompare(b.name));

    return NextResponse.json({ cranes: projectCranes });
  } catch (error) {
    console.error("[PROJECT_CRANES_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/projects/[projectId]/cranes - Assign a crane to a project (create booking)
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for project cranes POST API");

    const { projectId } = await params;
    const body = await req.json();
    const { craneId, startDate, endDate, notes } = body;

    if (!craneId) {
      return new NextResponse("Crane ID is required", { status: 400 });
    }

    if (!startDate || !endDate) {
      return new NextResponse("Start and end dates are required", { status: 400 });
    }

    // For testing, use a default user ID (we'll fix this later)
    const dbUser = { id: 1 };

    // Verify the project exists
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // Verify the crane exists
    const crane = await db.query.cranes.findFirst({
      where: and(eq(cranes.id, craneId), isNull(cranes.deletedAt)),
    });

    if (!crane) {
      return new NextResponse("Crane not found", { status: 404 });
    }

    // Parse dates
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // For now, skip conflict checking to get the basic functionality working
    // We'll implement proper conflict detection later

    // Create the booking in the database
    const newBooking = await db.insert(bookings).values({
      craneId,
      projectId,
      startDate: startDateObj,
      endDate: endDateObj,
      createdById: dbUser.id,
      notes: notes || null,
      status: "scheduled",
    }).returning();

    console.log("✅ Real booking created:", newBooking[0]);

    return NextResponse.json({ booking: newBooking[0] });
  } catch (error) {
    console.error("[PROJECT_CRANES_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
