import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { projects, cranes, bookings } from "@/lib/db/schema";
import { eq, and, isNull, sql } from "drizzle-orm";

// GET /api/projects/[projectId]/available-cranes - Get cranes available for assignment to a project
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for available cranes GET API");

    const { projectId } = await params;
    const { searchParams } = new URL(req.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Verify the project exists
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // Get all available cranes
    const allCranes = await db.query.cranes.findMany({
      where: and(
        isNull(cranes.deletedAt),
        eq(cranes.status, 'available')
      ),
      orderBy: [cranes.name],
    });

    // Get existing bookings for this project to check which cranes are already assigned
    const existingBookings = await db.query.bookings.findMany({
      where: and(
        eq(bookings.projectId, projectId),
        isNull(bookings.deletedAt)
      ),
    });

    // Process cranes to add assignment status
    const availableCranes = allCranes.map(crane => ({
      ...crane,
      already_assigned: existingBookings.some(booking => booking.craneId === crane.id)
    }));

    return NextResponse.json({
      cranes: availableCranes,
      filters: {
        startDate,
        endDate,
        projectId
      }
    });
  } catch (error) {
    console.error("[AVAILABLE_CRANES_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
