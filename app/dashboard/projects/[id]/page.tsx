import { currentUser } from '@clerk/nextjs/server';
import { redirect, notFound } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  Edit,
  MapPin,
  CalendarDays,
  Users,
  Truck,
  Calendar,
  Activity
} from 'lucide-react';
import ProjectCranes from './components/ProjectCranes';
import AssignedCranesCount from './components/AssignedCranesCount';

// Fetch project from API
async function getProject(id: string) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/projects/${id}`, {
      cache: 'no-store',
    });

    if (!response.ok) {
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching project:', error);
    return null;
  }
}

function getStatusColor(status: string) {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'completed':
      return 'bg-blue-100 text-blue-800';
    case 'on-hold':
      return 'bg-yellow-100 text-yellow-800';
    case 'cancelled':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-AU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

function ProjectOverview({ project }: { project: any }) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Project Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Description</label>
                <p className="mt-1">{project.description || 'No description provided'}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Location</label>
                <div className="flex items-center mt-1">
                  <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                  <span>{project.location?.address || 'Not specified'}</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Timeline</label>
                <div className="flex items-center mt-1">
                  <CalendarDays className="h-4 w-4 mr-2 text-gray-400" />
                  <span>
                    {project.startDate ? formatDate(project.startDate) : 'Not set'} - {project.endDate ? formatDate(project.endDate) : 'Not set'}
                  </span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Organization</label>
                <div className="flex items-center mt-1">
                  <Users className="h-4 w-4 mr-2 text-gray-400" />
                  <span>{project.organization?.name || 'Not specified'}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <AssignedCranesCard projectId={project.id} />

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Bookings</p>
                <p className="text-2xl font-bold">-</p>
                <p className="text-xs text-gray-400">Coming soon</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Team Members</p>
                <p className="text-2xl font-bold">-</p>
                <p className="text-xs text-gray-400">Coming soon</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function ProjectCranesWrapper({ projectId }: { projectId: string }) {
  return <ProjectCranes projectId={projectId} />;
}

function ProjectBookings() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Bookings & Schedule</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">Booking calendar interface will be implemented here.</p>
      </CardContent>
    </Card>
  );
}

function ProjectActivity() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">Activity log will be implemented here.</p>
      </CardContent>
    </Card>
  );
}

// Client component for assigned cranes card
function AssignedCranesCard({ projectId }: { projectId: string }) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center">
          <Truck className="h-8 w-8 text-blue-600" />
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-500">Assigned Cranes</p>
            <AssignedCranesCount projectId={projectId} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default async function ProjectDetailPage({
  params
}: {
  params: Promise<{ id: string }>
}) {
  const user = await currentUser();

  if (!user) {
    redirect('/sign-in');
  }

  const { id } = await params;
  const project = await getProject(id);
  
  if (!project) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/dashboard/projects">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Button>
          </Link>
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold">{project.name}</h1>
              <Badge className={getStatusColor(project.status)}>
                {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
              </Badge>
            </div>
            <p className="text-gray-600">
              Created on {formatDate(project.createdAt)}
            </p>
          </div>
        </div>
        <Link href={`/dashboard/projects/${project.id}/edit`}>
          <Button>
            <Edit className="h-4 w-4 mr-2" />
            Edit Project
          </Button>
        </Link>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="cranes">Cranes</TabsTrigger>
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <ProjectOverview project={project} />
        </TabsContent>
        
        <TabsContent value="cranes">
          <ProjectCranesWrapper projectId={project.id} />
        </TabsContent>
        
        <TabsContent value="bookings">
          <ProjectBookings />
        </TabsContent>
        
        <TabsContent value="activity">
          <ProjectActivity />
        </TabsContent>
      </Tabs>
    </div>
  );
}
