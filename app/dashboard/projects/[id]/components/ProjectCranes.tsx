'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Truck, Calendar, Trash2, AlertCircle, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';

interface Crane {
  id: number;
  name: string;
  model?: string;
  type: string;
  capacity?: string;
  height?: string;
  reach?: string;
  status: string;
  last_maintenance_date?: string;
  next_maintenance_date?: string;
  booking_count?: number;
  next_booking_start?: string;
  last_booking_end?: string;
  already_assigned?: boolean;
}

interface ProjectCranesProps {
  projectId: string;
}

export default function ProjectCranes({ projectId }: ProjectCranesProps) {
  const [assignedCranes, setAssignedCranes] = useState<Crane[]>([]);
  const [availableCranes, setAvailableCranes] = useState<Crane[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [selectedCrane, setSelectedCrane] = useState<number | null>(null);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [notes, setNotes] = useState('');
  const [isAssigning, setIsAssigning] = useState(false);

  // Fetch assigned cranes
  const fetchAssignedCranes = async () => {
    try {
      const response = await fetch(`/api/projects/${projectId}/cranes`);
      if (response.ok) {
        const data = await response.json();
        setAssignedCranes(data.cranes || []);
      }
    } catch (error) {
      console.error('Error fetching assigned cranes:', error);
    }
  };

  // Fetch available cranes
  const fetchAvailableCranes = async (start?: string, end?: string) => {
    try {
      const params = new URLSearchParams();
      if (start) params.append('startDate', start);
      if (end) params.append('endDate', end);
      
      const response = await fetch(`/api/projects/${projectId}/available-cranes?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAvailableCranes(data.cranes || []);
      }
    } catch (error) {
      console.error('Error fetching available cranes:', error);
    }
  };

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchAssignedCranes(),
        fetchAvailableCranes()
      ]);
      setLoading(false);
    };
    loadData();
  }, [projectId]);

  // Update available cranes when date range changes
  useEffect(() => {
    if (startDate && endDate) {
      fetchAvailableCranes(startDate, endDate);
    } else {
      fetchAvailableCranes();
    }
  }, [startDate, endDate]);

  // Assign crane to project
  const handleAssignCrane = async () => {
    if (!selectedCrane || !startDate || !endDate) {
      toast.error('Please select a crane and provide start/end dates');
      return;
    }

    setIsAssigning(true);
    try {
      const response = await fetch(`/api/projects/${projectId}/cranes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          craneId: selectedCrane,
          startDate,
          endDate,
          notes,
        }),
      });

      if (response.ok) {
        toast.success('Crane assigned successfully');
        setIsAssignDialogOpen(false);
        setSelectedCrane(null);
        setStartDate('');
        setEndDate('');
        setNotes('');
        await fetchAssignedCranes();
        await fetchAvailableCranes();
      } else {
        const error = await response.text();
        toast.error(error || 'Failed to assign crane');
      }
    } catch (error) {
      console.error('Error assigning crane:', error);
      toast.error('Failed to assign crane');
    } finally {
      setIsAssigning(false);
    }
  };

  // Remove crane assignment
  const handleRemoveCrane = async (craneId: number) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/cranes/${craneId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('Crane assignment removed');
        await fetchAssignedCranes();
        await fetchAvailableCranes();
      } else {
        const error = await response.text();
        toast.error(error || 'Failed to remove crane assignment');
      }
    } catch (error) {
      console.error('Error removing crane:', error);
      toast.error('Failed to remove crane assignment');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'offline':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Assigned Cranes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Assigned Cranes ({assignedCranes.length})
          </CardTitle>
          <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Assign Crane
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Assign Crane to Project</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startDate">Start Date</Label>
                    <Input
                      id="startDate"
                      type="datetime-local"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="endDate">End Date</Label>
                    <Input
                      id="endDate"
                      type="datetime-local"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="crane">Select Crane</Label>
                  <Select value={selectedCrane?.toString()} onValueChange={(value) => setSelectedCrane(parseInt(value))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a crane" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableCranes.map((crane) => (
                        <SelectItem key={crane.id} value={crane.id.toString()}>
                          <div className="flex items-center justify-between w-full">
                            <span>{crane.name} ({crane.type})</span>
                            {crane.already_assigned && (
                              <Badge variant="secondary" className="ml-2">Already Assigned</Badge>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="notes">Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    placeholder="Add any notes about this assignment..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAssignCrane} disabled={isAssigning}>
                    {isAssigning ? 'Assigning...' : 'Assign Crane'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          {assignedCranes.length === 0 ? (
            <div className="text-center py-8">
              <Truck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No cranes assigned to this project yet.</p>
              <Button onClick={() => setIsAssignDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Assign First Crane
              </Button>
            </div>
          ) : (
            <div className="grid gap-4">
              {assignedCranes.map((crane) => (
                <div key={crane.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <h3 className="font-semibold">{crane.name}</h3>
                      <Badge className={getStatusColor(crane.status)}>
                        {crane.status}
                      </Badge>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveCrane(crane.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Type:</span>
                      <p className="font-medium">{crane.type}</p>
                    </div>
                    {crane.capacity && (
                      <div>
                        <span className="text-gray-500">Capacity:</span>
                        <p className="font-medium">{crane.capacity} tons</p>
                      </div>
                    )}
                    {crane.booking_count && (
                      <div>
                        <span className="text-gray-500">Active Bookings:</span>
                        <p className="font-medium">{crane.booking_count}</p>
                      </div>
                    )}
                    {crane.next_booking_start && (
                      <div>
                        <span className="text-gray-500">Next Booking:</span>
                        <p className="font-medium">{formatDate(crane.next_booking_start)}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
