'use client';

import { useState, useEffect } from 'react';

interface AssignedCrane {
  id: number;
  name: string;
  type: string;
  booking_count: number;
}

export default function AssignedCranesCount({ projectId }: { projectId: string }) {
  const [cranes, setCranes] = useState<AssignedCrane[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchAssignedCranes() {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/projects/${projectId}/cranes`);
        if (!response.ok) {
          throw new Error('Failed to fetch assigned cranes');
        }
        
        const data = await response.json();
        setCranes(data.cranes || []);
      } catch (err) {
        console.error('Error fetching assigned cranes:', err);
        setError(err instanceof Error ? err.message : 'Failed to load');
      } finally {
        setLoading(false);
      }
    }

    fetchAssignedCranes();
  }, [projectId]);

  if (loading) {
    return (
      <>
        <p className="text-2xl font-bold text-gray-400">...</p>
        <p className="text-xs text-gray-400">Loading...</p>
      </>
    );
  }

  if (error) {
    return (
      <>
        <p className="text-2xl font-bold text-red-600">Error</p>
        <p className="text-xs text-red-400">Failed to load</p>
      </>
    );
  }

  const count = cranes.length;
  const totalBookings = cranes.reduce((sum, crane) => sum + crane.booking_count, 0);

  return (
    <>
      <p className="text-2xl font-bold">{count}</p>
      <p className="text-xs text-gray-400">
        {count === 0 
          ? 'No cranes assigned' 
          : count === 1 
            ? `1 crane, ${totalBookings} booking${totalBookings !== 1 ? 's' : ''}`
            : `${count} cranes, ${totalBookings} booking${totalBookings !== 1 ? 's' : ''}`
        }
      </p>
    </>
  );
}
