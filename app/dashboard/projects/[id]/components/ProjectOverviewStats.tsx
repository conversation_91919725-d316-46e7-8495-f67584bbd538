'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Truck, Calendar, Users } from 'lucide-react';

interface AssignedCrane {
  id: number;
  name: string;
  type: string;
  booking_count: number;
  next_booking_start: string | null;
  last_booking_end: string | null;
}

interface ProjectStats {
  assignedCranes: AssignedCrane[];
  totalBookings: number;
  activeBookings: number;
}

export default function ProjectOverviewStats({ projectId }: { projectId: string }) {
  const [stats, setStats] = useState<ProjectStats>({
    assignedCranes: [],
    totalBookings: 0,
    activeBookings: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchStats() {
      try {
        setLoading(true);
        setError(null);

        // Fetch assigned cranes
        const cranesResponse = await fetch(`/api/projects/${projectId}/cranes`);
        if (!cranesResponse.ok) {
          throw new Error('Failed to fetch assigned cranes');
        }
        const cranesData = await cranesResponse.json();
        
        // Calculate stats
        const assignedCranes = cranesData.cranes || [];
        const totalBookings = assignedCranes.reduce((sum: number, crane: AssignedCrane) => sum + crane.booking_count, 0);
        
        // For now, consider all bookings as active (we can enhance this later with date filtering)
        const activeBookings = totalBookings;

        setStats({
          assignedCranes,
          totalBookings,
          activeBookings,
        });
      } catch (err) {
        console.error('Error fetching project stats:', err);
        setError(err instanceof Error ? err.message : 'Failed to load stats');
      } finally {
        setLoading(false);
      }
    }

    fetchStats();
  }, [projectId]);

  const formatNextBooking = (dateString: string | null) => {
    if (!dateString) return 'No upcoming bookings';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Booking in progress';
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays < 7) return `In ${diffDays} days`;
    
    return date.toLocaleDateString('en-AU', {
      month: 'short',
      day: 'numeric',
    });
  };

  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Truck className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Assigned Cranes</p>
                <p className="text-2xl font-bold text-red-600">Error</p>
                <p className="text-xs text-red-400">Failed to load</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Bookings</p>
                <p className="text-2xl font-bold text-red-600">Error</p>
                <p className="text-xs text-red-400">Failed to load</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Team Members</p>
                <p className="text-2xl font-bold">-</p>
                <p className="text-xs text-gray-400">Coming soon</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <Truck className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Assigned Cranes</p>
              {loading ? (
                <>
                  <p className="text-2xl font-bold text-gray-400">...</p>
                  <p className="text-xs text-gray-400">Loading...</p>
                </>
              ) : (
                <>
                  <p className="text-2xl font-bold">{stats.assignedCranes.length}</p>
                  <p className="text-xs text-gray-400">
                    {stats.assignedCranes.length === 0 
                      ? 'No cranes assigned' 
                      : stats.assignedCranes.length === 1 
                        ? '1 crane assigned'
                        : `${stats.assignedCranes.length} cranes assigned`
                    }
                  </p>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <Calendar className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Bookings</p>
              {loading ? (
                <>
                  <p className="text-2xl font-bold text-gray-400">...</p>
                  <p className="text-xs text-gray-400">Loading...</p>
                </>
              ) : (
                <>
                  <p className="text-2xl font-bold">{stats.activeBookings}</p>
                  <p className="text-xs text-gray-400">
                    {stats.activeBookings === 0 
                      ? 'No active bookings' 
                      : stats.activeBookings === 1 
                        ? '1 active booking'
                        : `${stats.activeBookings} active bookings`
                    }
                  </p>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Team Members</p>
              <p className="text-2xl font-bold">-</p>
              <p className="text-xs text-gray-400">Coming soon</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
