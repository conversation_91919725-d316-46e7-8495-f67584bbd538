'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, Calendar, Clock, Truck, MapPin, User, Package } from 'lucide-react';

interface LiftBooking {
  id: number;
  craneId: number;
  craneName: string;
  projectId: string;
  projectName: string;
  liftDate: string;
  startTime: string;
  endTime: string;
  numberOfLifts: number;
  totalDuration: number;
  liftPurpose: string;
  itemDescription: string;
  pickupLocation?: string;
  dropoffLocation: string;
  estimatedWeight?: number;
  status: string;
  contactPerson: string;
  contactPhone?: string;
  bookedBy: {
    id: number;
    name: string;
    email: string;
  };
}

export default function LiftScheduleCalendar() {
  const [liftBookings, setLiftBookings] = useState<LiftBooking[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [loading, setLoading] = useState(true);

  // Format date for API calls
  const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  // Fetch lift bookings for selected date
  useEffect(() => {
    const fetchLiftBookings = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/lift-bookings?date=${formatDate(selectedDate)}`);
        if (response.ok) {
          const data = await response.json();
          setLiftBookings(data.liftBookings || []);
        }
      } catch (error) {
        console.error('Error fetching lift bookings:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLiftBookings();
  }, [selectedDate]);

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    setSelectedDate(newDate);
  };

  const goToToday = () => {
    setSelectedDate(new Date());
  };

  const formatDateHeader = () => {
    return selectedDate.toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'in-progress':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPurposeIcon = (purpose: string) => {
    switch (purpose) {
      case 'materials':
        return <Package className="h-4 w-4" />;
      case 'equipment':
        return <Truck className="h-4 w-4" />;
      case 'personnel':
        return <User className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  // Group bookings by crane
  const bookingsByCrane = liftBookings.reduce((acc, booking) => {
    if (!acc[booking.craneName]) {
      acc[booking.craneName] = [];
    }
    acc[booking.craneName].push(booking);
    return acc;
  }, {} as Record<string, LiftBooking[]>);

  // Sort bookings by start time within each crane
  Object.keys(bookingsByCrane).forEach(craneName => {
    bookingsByCrane[craneName].sort((a, b) => a.startTime.localeCompare(b.startTime));
  });

  if (loading) {
    return (
      <div className="h-96 flex items-center justify-center">
        <div className="text-center">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Loading lift schedule...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Date Navigation */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateDate('prev')}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-xl font-semibold min-w-[250px] text-center">
              {formatDateHeader()}
            </h2>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateDate('next')}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={goToToday}
          >
            Today
          </Button>
        </div>

        <div className="text-sm text-gray-600">
          {liftBookings.length} lift booking{liftBookings.length !== 1 ? 's' : ''} scheduled
        </div>
      </div>

      {/* Schedule Content */}
      {Object.keys(bookingsByCrane).length === 0 ? (
        <Card>
          <CardContent className="p-8">
            <div className="text-center">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No lift bookings</h3>
              <p className="text-gray-500">
                No crane lifts are scheduled for {formatDateHeader().toLowerCase()}
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {Object.entries(bookingsByCrane).map(([craneName, bookings]) => (
            <Card key={craneName}>
              <CardContent className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Truck className="h-5 w-5 text-blue-600" />
                  <h3 className="text-lg font-semibold">{craneName}</h3>
                  <Badge variant="outline">
                    {bookings.length} lift{bookings.length !== 1 ? 's' : ''}
                  </Badge>
                </div>

                <div className="space-y-3">
                  {bookings.map((booking) => (
                    <div
                      key={booking.id}
                      className="flex items-start justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex-1 space-y-2">
                        {/* Time and Duration */}
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1 text-sm font-medium">
                            <Clock className="h-4 w-4 text-gray-500" />
                            {booking.startTime} - {booking.endTime}
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {booking.numberOfLifts} lift{booking.numberOfLifts !== 1 ? 's' : ''} 
                            ({booking.totalDuration} min)
                          </Badge>
                        </div>

                        {/* Item and Purpose */}
                        <div className="flex items-center gap-2">
                          {getPurposeIcon(booking.liftPurpose)}
                          <span className="font-medium">{booking.itemDescription}</span>
                          <span className="text-sm text-gray-500 capitalize">({booking.liftPurpose})</span>
                          {booking.estimatedWeight && (
                            <span className="text-sm text-gray-500">
                              • {booking.estimatedWeight}t
                            </span>
                          )}
                        </div>

                        {/* Locations */}
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            <span>From: {booking.pickupLocation || 'Ground level'}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            <span>To: {booking.dropoffLocation}</span>
                          </div>
                        </div>

                        {/* Contact and Project */}
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>Contact: {booking.contactPerson}</span>
                          {booking.contactPhone && <span>• {booking.contactPhone}</span>}
                          <span>• {booking.projectName}</span>
                        </div>
                      </div>

                      {/* Status and Actions */}
                      <div className="flex flex-col items-end gap-2">
                        <Badge className={getStatusColor(booking.status)}>
                          {booking.status.replace('-', ' ')}
                        </Badge>
                        <div className="text-xs text-gray-500">
                          ID: {booking.id}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
