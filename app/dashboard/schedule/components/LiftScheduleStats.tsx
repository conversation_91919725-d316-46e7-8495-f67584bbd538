'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Truck,
  Package,
  TrendingUp,
  Users
} from 'lucide-react';

interface LiftStats {
  totalLifts: number;
  pendingLifts: number;
  confirmedLifts: number;
  completedLifts: number;
  todayLifts: number;
  tomorrowLifts: number;
  totalDuration: number;
  averageDuration: number;
}

export default function LiftScheduleStats() {
  const [stats, setStats] = useState<LiftStats>({
    totalLifts: 0,
    pendingLifts: 0,
    confirmedLifts: 0,
    completedLifts: 0,
    todayLifts: 0,
    tomorrowLifts: 0,
    totalDuration: 0,
    averageDuration: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/lift-bookings');
        if (response.ok) {
          const data = await response.json();
          const liftBookings = data.liftBookings || [];
          
          const now = new Date();
          const today = now.toISOString().split('T')[0];
          const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
          
          const totalLifts = liftBookings.length;
          const pendingLifts = liftBookings.filter((b: any) => b.status === 'pending').length;
          const confirmedLifts = liftBookings.filter((b: any) => b.status === 'confirmed').length;
          const completedLifts = liftBookings.filter((b: any) => b.status === 'completed').length;
          const todayLifts = liftBookings.filter((b: any) => b.liftDate === today).length;
          const tomorrowLifts = liftBookings.filter((b: any) => b.liftDate === tomorrow).length;
          
          // Calculate total duration in minutes
          const totalDuration = liftBookings.reduce((total: number, booking: any) => {
            return total + (booking.numberOfLifts * (booking.slotDuration || 30));
          }, 0);
          
          const averageDuration = totalLifts > 0 ? totalDuration / totalLifts : 0;
          
          setStats({
            totalLifts,
            pendingLifts,
            confirmedLifts,
            completedLifts,
            todayLifts,
            tomorrowLifts,
            totalDuration: Math.round(totalDuration),
            averageDuration: Math.round(averageDuration)
          });
        }
      } catch (error) {
        console.error('Error fetching lift booking stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      title: 'Total Lifts',
      value: stats.totalLifts,
      icon: Truck,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Pending',
      value: stats.pendingLifts,
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    },
    {
      title: 'Confirmed',
      value: stats.confirmedLifts,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Today',
      value: stats.todayLifts,
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Tomorrow',
      value: stats.tomorrowLifts,
      icon: AlertCircle,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      title: 'Avg Duration',
      value: `${stats.averageDuration}m`,
      icon: TrendingUp,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100'
    }
  ];

  if (loading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-6 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                </div>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-5 w-5 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
