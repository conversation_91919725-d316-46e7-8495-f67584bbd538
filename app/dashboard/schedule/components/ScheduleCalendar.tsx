'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Calendar, List } from 'lucide-react';
import BookingDetailModal from './BookingDetailModal';

interface Booking {
  id: number;
  craneId: number;
  craneName: string;
  projectId: string;
  projectName: string;
  startDate: string;
  endDate: string;
  status: string;
  notes?: string;
}

interface CalendarEvent {
  id: number;
  title: string;
  start: Date;
  end: Date;
  resource: string;
  status: string;
  booking: Booking;
}

export default function ScheduleCalendar() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month');
  const [loading, setLoading] = useState(true);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [detailModalOpen, setDetailModalOpen] = useState(false);

  // Fetch bookings from API
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/bookings');
        if (response.ok) {
          const data = await response.json();
          setBookings(data.bookings || []);
          
          // Convert bookings to calendar events
          const calendarEvents: CalendarEvent[] = (data.bookings || []).map((booking: Booking) => ({
            id: booking.id,
            title: `${booking.craneName} - ${booking.projectName}`,
            start: new Date(booking.startDate),
            end: new Date(booking.endDate),
            resource: booking.craneName,
            status: booking.status,
            booking
          }));
          
          setEvents(calendarEvents);
        }
      } catch (error) {
        console.error('Error fetching bookings:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBookings();
  }, []);

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    
    switch (viewMode) {
      case 'month':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
      case 'week':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'day':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
    }
    
    setCurrentDate(newDate);
  };

  const formatDateHeader = () => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      ...(viewMode === 'day' && { day: 'numeric' })
    };
    
    return currentDate.toLocaleDateString('en-AU', options);
  };

  if (loading) {
    return (
      <div className="h-96 flex items-center justify-center">
        <div className="text-center">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Loading schedule...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Calendar Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateDate('prev')}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-xl font-semibold min-w-[200px] text-center">
              {formatDateHeader()}
            </h2>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateDate('next')}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentDate(new Date())}
          >
            Today
          </Button>
        </div>

        {/* View Mode Selector */}
        <div className="flex gap-1 bg-gray-100 rounded-lg p-1">
          {(['month', 'week', 'day'] as const).map((mode) => (
            <Button
              key={mode}
              variant={viewMode === mode ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode(mode)}
              className="capitalize"
            >
              {mode}
            </Button>
          ))}
        </div>
      </div>

      {/* Calendar Content */}
      <Card>
        <CardContent className="p-6">
          {events.length === 0 ? (
            <div className="h-64 flex items-center justify-center text-center">
              <div>
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
                <p className="text-gray-500">
                  Create your first booking to see it on the schedule
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-sm text-gray-600 mb-4">
                Showing {events.length} booking{events.length !== 1 ? 's' : ''}
              </div>
              
              {/* Enhanced booking list view */}
              <div className="space-y-3">
                {events.map((event) => (
                  <div
                    key={event.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                    onClick={() => {
                      setSelectedBooking(event.booking);
                      setDetailModalOpen(true);
                    }}
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium text-lg">{event.booking.craneName}</h4>
                        <span className="text-sm text-gray-500">•</span>
                        <span className="text-sm text-gray-600">{event.booking.projectName}</span>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>
                            {event.start.toLocaleDateString('en-AU', {
                              weekday: 'short',
                              month: 'short',
                              day: 'numeric'
                            })}
                          </span>
                        </div>
                        <div>
                          {event.start.toLocaleTimeString('en-AU', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })} - {event.end.toLocaleTimeString('en-AU', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                        {event.booking.notes && (
                          <div className="text-xs text-gray-500 truncate max-w-xs">
                            "{event.booking.notes}"
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        event.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                        event.status === 'in-progress' ? 'bg-yellow-100 text-yellow-800' :
                        event.status === 'completed' ? 'bg-green-100 text-green-800' :
                        event.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {event.status.replace('-', ' ')}
                      </span>

                      <div className="text-right text-xs text-gray-500">
                        <div>ID: {event.id}</div>
                        <div>
                          {Math.ceil((event.end.getTime() - event.start.getTime()) / (1000 * 60 * 60))}h duration
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Booking Detail Modal */}
      <BookingDetailModal
        booking={selectedBooking}
        open={detailModalOpen}
        onOpenChange={setDetailModalOpen}
        onBookingUpdated={() => {
          // Refresh bookings when a booking is updated
          const fetchBookings = async () => {
            try {
              const response = await fetch('/api/bookings');
              if (response.ok) {
                const data = await response.json();
                setBookings(data.bookings || []);

                // Convert bookings to calendar events
                const calendarEvents: CalendarEvent[] = (data.bookings || []).map((booking: Booking) => ({
                  id: booking.id,
                  title: `${booking.craneName} - ${booking.projectName}`,
                  start: new Date(booking.startDate),
                  end: new Date(booking.endDate),
                  resource: booking.craneName,
                  status: booking.status,
                  booking
                }));

                setEvents(calendarEvents);
              }
            } catch (error) {
              console.error('Error refreshing bookings:', error);
            }
          };
          fetchBookings();
        }}
      />
    </div>
  );
}
