'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

interface Project {
  id: string;
  name: string;
}

interface Crane {
  id: number;
  name: string;
  type: string;
  status: string;
}

interface CreateBookingDialogProps {
  children: React.ReactNode;
}

export default function CreateBookingDialog({ children }: CreateBookingDialogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [cranes, setCranes] = useState<Crane[]>([]);
  const [availableCranes, setAvailableCranes] = useState<Crane[]>([]);

  const [formData, setFormData] = useState({
    projectId: '',
    craneId: '',
    startDate: '',
    endDate: '',
    notes: '',
    status: 'scheduled'
  });

  // Fetch projects and cranes
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [projectsRes, cranesRes] = await Promise.all([
          fetch('/api/projects'),
          fetch('/api/cranes')
        ]);

        if (projectsRes.ok) {
          const projectsData = await projectsRes.json();
          setProjects(projectsData.projects || []);
        }

        if (cranesRes.ok) {
          const cranesData = await cranesRes.json();
          setCranes(cranesData.cranes || []);
          setAvailableCranes(cranesData.cranes || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load projects and cranes');
      }
    };

    if (open) {
      fetchData();
    }
  }, [open]);

  // Check crane availability when dates change
  useEffect(() => {
    const checkAvailability = async () => {
      if (!formData.startDate || !formData.endDate) {
        setAvailableCranes(cranes);
        return;
      }

      try {
        const availabilityPromises = cranes.map(async (crane) => {
          const response = await fetch('/api/bookings/availability', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              craneId: crane.id,
              startDate: formData.startDate,
              endDate: formData.endDate
            })
          });

          if (response.ok) {
            const data = await response.json();
            return { crane, available: data.available };
          }
          return { crane, available: false };
        });

        const results = await Promise.all(availabilityPromises);
        const available = results
          .filter(result => result.available)
          .map(result => result.crane);

        setAvailableCranes(available);

        // Clear crane selection if currently selected crane is not available
        if (formData.craneId && !available.find(crane => crane.id.toString() === formData.craneId)) {
          setFormData(prev => ({ ...prev, craneId: '' }));
        }
      } catch (error) {
        console.error('Error checking availability:', error);
        setAvailableCranes(cranes);
      }
    };

    checkAvailability();
  }, [formData.startDate, formData.endDate, cranes]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.projectId || !formData.craneId || !formData.startDate || !formData.endDate) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (formData.craneId === 'no-cranes-available') {
      toast.error('Please select a valid crane');
      return;
    }

    if (new Date(formData.startDate) >= new Date(formData.endDate)) {
      toast.error('End date must be after start date');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId: formData.projectId,
          craneId: parseInt(formData.craneId),
          startDate: formData.startDate,
          endDate: formData.endDate,
          notes: formData.notes,
          status: formData.status
        })
      });

      if (response.ok) {
        toast.success('Booking created successfully');
        setOpen(false);
        setFormData({
          projectId: '',
          craneId: '',
          startDate: '',
          endDate: '',
          notes: '',
          status: 'scheduled'
        });
        setAvailableCranes(cranes); // Reset available cranes
        // Refresh the page to show new booking
        window.location.reload();
      } else {
        const error = await response.text();
        toast.error(error || 'Failed to create booking');
      }
    } catch (error) {
      console.error('Error creating booking:', error);
      toast.error('Failed to create booking');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Booking</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="project">Project *</Label>
            <Select value={formData.projectId} onValueChange={(value) => handleInputChange('projectId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a project" />
              </SelectTrigger>
              <SelectContent>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date *</Label>
              <Input
                id="startDate"
                type="datetime-local"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">End Date *</Label>
              <Input
                id="endDate"
                type="datetime-local"
                value={formData.endDate}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="crane">Crane *</Label>
            <Select value={formData.craneId} onValueChange={(value) => handleInputChange('craneId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a crane" />
              </SelectTrigger>
              <SelectContent>
                {availableCranes.length === 0 ? (
                  <SelectItem value="no-cranes-available" disabled>
                    No cranes available for selected dates
                  </SelectItem>
                ) : (
                  availableCranes.map((crane) => (
                    <SelectItem key={crane.id} value={crane.id.toString()}>
                      {crane.name} ({crane.type})
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
            {formData.startDate && formData.endDate && availableCranes.length === 0 && (
              <p className="text-sm text-red-600">
                No cranes available for the selected time period
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Add any additional notes..."
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Create Booking
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
