'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  TrendingUp,
  Users
} from 'lucide-react';

interface BookingStats {
  totalBookings: number;
  activeBookings: number;
  completedBookings: number;
  upcomingBookings: number;
  totalHours: number;
  averageDuration: number;
}

export default function ScheduleStats() {
  const [stats, setStats] = useState<BookingStats>({
    totalBookings: 0,
    activeBookings: 0,
    completedBookings: 0,
    upcomingBookings: 0,
    totalHours: 0,
    averageDuration: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/bookings');
        if (response.ok) {
          const data = await response.json();
          const bookings = data.bookings || [];
          
          const now = new Date();
          const totalBookings = bookings.length;
          const activeBookings = bookings.filter((b: any) => b.status === 'in-progress').length;
          const completedBookings = bookings.filter((b: any) => b.status === 'completed').length;
          const upcomingBookings = bookings.filter((b: any) => 
            b.status === 'scheduled' && new Date(b.startDate) > now
          ).length;
          
          // Calculate total hours
          const totalHours = bookings.reduce((total: number, booking: any) => {
            const start = new Date(booking.startDate);
            const end = new Date(booking.endDate);
            const hours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
            return total + hours;
          }, 0);
          
          const averageDuration = totalBookings > 0 ? totalHours / totalBookings : 0;
          
          setStats({
            totalBookings,
            activeBookings,
            completedBookings,
            upcomingBookings,
            totalHours: Math.round(totalHours),
            averageDuration: Math.round(averageDuration * 10) / 10
          });
        }
      } catch (error) {
        console.error('Error fetching booking stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = [
    {
      title: 'Total Bookings',
      value: stats.totalBookings,
      icon: Calendar,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Active Now',
      value: stats.activeBookings,
      icon: AlertCircle,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    },
    {
      title: 'Completed',
      value: stats.completedBookings,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Upcoming',
      value: stats.upcomingBookings,
      icon: Clock,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Total Hours',
      value: `${stats.totalHours}h`,
      icon: TrendingUp,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100'
    },
    {
      title: 'Avg Duration',
      value: `${stats.averageDuration}h`,
      icon: Users,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100'
    }
  ];

  if (loading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-6 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                </div>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-5 w-5 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
