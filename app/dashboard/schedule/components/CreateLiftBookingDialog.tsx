'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { Loader2, Clock, Truck, MapPin } from 'lucide-react';

interface Project {
  id: string;
  name: string;
}

interface Crane {
  id: number;
  name: string;
  type: string;
  status: string;
  assignedProjectId: string;
  siteLocation?: string;
}

interface TimeSlot {
  startTime: string;
  endTime: string;
  available: boolean;
}

interface CreateLiftBookingDialogProps {
  children: React.ReactNode;
}

export default function CreateLiftBookingDialog({ children }: CreateLiftBooking<PERSON><PERSON>ogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [cranes, setCranes] = useState<Crane[]>([]);
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [checkingAvailability, setCheckingAvailability] = useState(false);

  const [formData, setFormData] = useState({
    projectId: '',
    craneId: '',
    liftDate: '',
    startTime: '',
    numberOfLifts: '1',
    liftPurpose: '',
    itemDescription: '',
    pickupLocation: '',
    dropoffLocation: '',
    estimatedWeight: '',
    specialRequirements: '',
    contactPerson: '',
    contactPhone: '',
  });

  // Fetch projects and cranes
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [projectsRes, cranesRes] = await Promise.all([
          fetch('/api/projects'),
          fetch('/api/cranes')
        ]);

        if (projectsRes.ok) {
          const projectsData = await projectsRes.json();
          setProjects(projectsData.projects || []);
        }

        if (cranesRes.ok) {
          const cranesData = await cranesRes.json();
          setCranes(cranesData.cranes || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load projects and cranes');
      }
    };

    if (open) {
      fetchData();
    }
  }, [open]);

  // Check availability when crane, date, or number of lifts changes
  useEffect(() => {
    const checkAvailability = async () => {
      if (!formData.craneId || !formData.liftDate || !formData.numberOfLifts) {
        setAvailableSlots([]);
        return;
      }

      setCheckingAvailability(true);
      try {
        const response = await fetch('/api/lift-bookings/availability', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            craneId: formData.craneId,
            liftDate: formData.liftDate,
            numberOfLifts: parseInt(formData.numberOfLifts)
          })
        });

        if (response.ok) {
          const data = await response.json();
          setAvailableSlots(data.availableSlots || []);
        } else {
          setAvailableSlots([]);
        }
      } catch (error) {
        console.error('Error checking availability:', error);
        setAvailableSlots([]);
      } finally {
        setCheckingAvailability(false);
      }
    };

    checkAvailability();
  }, [formData.craneId, formData.liftDate, formData.numberOfLifts]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    const requiredFields = [
      'projectId', 'craneId', 'liftDate', 'startTime', 'numberOfLifts',
      'liftPurpose', 'itemDescription', 'dropoffLocation', 'contactPerson'
    ];
    
    for (const field of requiredFields) {
      if (!formData[field as keyof typeof formData]) {
        toast.error(`Please fill in ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
        return;
      }
    }

    // Check if selected time slot is available
    const selectedSlot = availableSlots.find(slot => slot.startTime === formData.startTime);
    if (!selectedSlot || !selectedSlot.available) {
      toast.error('Selected time slot is not available');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/lift-bookings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          craneId: parseInt(formData.craneId),
          projectId: formData.projectId,
          liftDate: formData.liftDate,
          startTime: formData.startTime,
          numberOfLifts: parseInt(formData.numberOfLifts),
          liftPurpose: formData.liftPurpose,
          itemDescription: formData.itemDescription,
          pickupLocation: formData.pickupLocation,
          dropoffLocation: formData.dropoffLocation,
          estimatedWeight: formData.estimatedWeight ? parseFloat(formData.estimatedWeight) : null,
          specialRequirements: formData.specialRequirements,
          contactPerson: formData.contactPerson,
          contactPhone: formData.contactPhone,
        })
      });

      if (response.ok) {
        toast.success('Lift booking created successfully');
        setOpen(false);
        resetForm();
        // Refresh the page to show new booking
        window.location.reload();
      } else {
        const error = await response.text();
        toast.error(error || 'Failed to create lift booking');
      }
    } catch (error) {
      console.error('Error creating lift booking:', error);
      toast.error('Failed to create lift booking');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      projectId: '',
      craneId: '',
      liftDate: '',
      startTime: '',
      numberOfLifts: '1',
      liftPurpose: '',
      itemDescription: '',
      pickupLocation: '',
      dropoffLocation: '',
      estimatedWeight: '',
      specialRequirements: '',
      contactPerson: '',
      contactPhone: '',
    });
    setAvailableSlots([]);
  };

  // Get cranes for selected project
  const projectCranes = cranes.filter(crane => 
    !formData.projectId || crane.assignedProjectId === formData.projectId
  );

  // Get today's date for minimum date selection
  const today = new Date().toISOString().split('T')[0];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Book Crane Lift Time
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Project and Crane Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="project">Project *</Label>
              <Select value={formData.projectId} onValueChange={(value) => handleInputChange('projectId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="crane">Crane *</Label>
              <Select value={formData.craneId} onValueChange={(value) => handleInputChange('craneId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a crane" />
                </SelectTrigger>
                <SelectContent>
                  {projectCranes.length === 0 ? (
                    <SelectItem value="no-cranes" disabled>
                      {formData.projectId ? 'No cranes assigned to this project' : 'Select a project first'}
                    </SelectItem>
                  ) : (
                    projectCranes.map((crane) => (
                      <SelectItem key={crane.id} value={crane.id.toString()}>
                        {crane.name} ({crane.type})
                        {crane.siteLocation && ` - ${crane.siteLocation}`}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Date and Time Selection */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="liftDate">Lift Date *</Label>
              <Input
                id="liftDate"
                type="date"
                min={today}
                value={formData.liftDate}
                onChange={(e) => handleInputChange('liftDate', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="numberOfLifts">Number of Lifts *</Label>
              <Select value={formData.numberOfLifts} onValueChange={(value) => handleInputChange('numberOfLifts', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => (
                    <SelectItem key={num} value={num.toString()}>
                      {num} lift{num > 1 ? 's' : ''} ({num * 30} min)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="startTime">Start Time *</Label>
              <Select value={formData.startTime} onValueChange={(value) => handleInputChange('startTime', value)}>
                <SelectTrigger>
                  <SelectValue placeholder={checkingAvailability ? "Checking..." : "Select time"} />
                </SelectTrigger>
                <SelectContent>
                  {checkingAvailability ? (
                    <SelectItem value="loading" disabled>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Checking availability...
                    </SelectItem>
                  ) : availableSlots.length === 0 ? (
                    <SelectItem value="no-slots" disabled>
                      No available slots
                    </SelectItem>
                  ) : (
                    availableSlots.map((slot) => (
                      <SelectItem 
                        key={slot.startTime} 
                        value={slot.startTime}
                        disabled={!slot.available}
                      >
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          {slot.startTime} - {slot.endTime}
                          {!slot.available && ' (Unavailable)'}
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Lift Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Lift Details</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="liftPurpose">Lift Purpose *</Label>
                <Select value={formData.liftPurpose} onValueChange={(value) => handleInputChange('liftPurpose', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="What are you lifting?" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="materials">Materials</SelectItem>
                    <SelectItem value="equipment">Equipment</SelectItem>
                    <SelectItem value="personnel">Personnel</SelectItem>
                    <SelectItem value="waste">Waste Removal</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="estimatedWeight">Estimated Weight (tonnes)</Label>
                <Input
                  id="estimatedWeight"
                  type="number"
                  step="0.1"
                  placeholder="e.g., 2.5"
                  value={formData.estimatedWeight}
                  onChange={(e) => handleInputChange('estimatedWeight', e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="itemDescription">Item Description *</Label>
              <Textarea
                id="itemDescription"
                placeholder="Describe what you're lifting (e.g., Steel beams, Concrete panels, Tools)"
                value={formData.itemDescription}
                onChange={(e) => handleInputChange('itemDescription', e.target.value)}
                rows={2}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="pickupLocation">Pickup Location</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="pickupLocation"
                    placeholder="e.g., Ground level, Loading bay"
                    value={formData.pickupLocation}
                    onChange={(e) => handleInputChange('pickupLocation', e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="dropoffLocation">Drop-off Location *</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="dropoffLocation"
                    placeholder="e.g., Level 5, Roof, East wing"
                    value={formData.dropoffLocation}
                    onChange={(e) => handleInputChange('dropoffLocation', e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="specialRequirements">Special Requirements</Label>
              <Textarea
                id="specialRequirements"
                placeholder="Any special handling, safety requirements, or notes"
                value={formData.specialRequirements}
                onChange={(e) => handleInputChange('specialRequirements', e.target.value)}
                rows={2}
              />
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Contact Information</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contactPerson">Contact Person *</Label>
                <Input
                  id="contactPerson"
                  placeholder="Your name"
                  value={formData.contactPerson}
                  onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="contactPhone">Contact Phone</Label>
                <Input
                  id="contactPhone"
                  type="tel"
                  placeholder="Your phone number"
                  value={formData.contactPhone}
                  onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading || checkingAvailability}>
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Book Lift Time
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
