import { Suspense } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, Plus, Filter, Download, Clock, Truck } from 'lucide-react';
import ScheduleCalendar from './components/ScheduleCalendar';
import BookingFilters from './components/BookingFilters';
import CreateBookingDialog from './components/CreateBookingDialog';
import ScheduleStats from './components/ScheduleStats';

export default function SchedulePage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Truck className="h-8 w-8 text-blue-600" />
            Crane Lift Schedule
          </h1>
          <p className="text-gray-600 mt-1">
            Book crane lift times for materials and equipment movement on construction sites
          </p>
          <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>30-minute lift slots</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <span>Book multiple consecutive lifts</span>
            </div>
            <div className="flex items-center gap-1">
              <Truck className="h-4 w-4" />
              <span>Subcontractor lift booking</span>
            </div>
          </div>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <CreateBookingDialog>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Book Lift Time
            </Button>
          </CreateBookingDialog>
        </div>
      </div>

      {/* Stats */}
      <Suspense fallback={<div>Loading stats...</div>}>
        <ScheduleStats />
      </Suspense>

      {/* Filters */}
      <Suspense fallback={<div>Loading filters...</div>}>
        <BookingFilters />
      </Suspense>

      {/* Calendar */}
      <Card>
        <CardHeader>
          <CardTitle>Daily Lift Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div className="h-96 flex items-center justify-center">Loading schedule...</div>}>
            <ScheduleCalendar />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
