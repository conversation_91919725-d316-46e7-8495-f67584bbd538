import { Suspense } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, Plus, Filter, Download } from 'lucide-react';
import ScheduleCalendar from './components/ScheduleCalendar';
import BookingFilters from './components/BookingFilters';
import CreateBookingDialog from './components/CreateBookingDialog';
import ScheduleStats from './components/ScheduleStats';

export default function SchedulePage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Calendar className="h-8 w-8 text-blue-600" />
            Schedule
          </h1>
          <p className="text-gray-600 mt-1">
            Manage crane bookings and schedules across all projects
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <CreateBookingDialog>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Booking
            </Button>
          </CreateBookingDialog>
        </div>
      </div>

      {/* Stats */}
      <Suspense fallback={<div>Loading stats...</div>}>
        <ScheduleStats />
      </Suspense>

      {/* Filters */}
      <Suspense fallback={<div>Loading filters...</div>}>
        <BookingFilters />
      </Suspense>

      {/* Calendar */}
      <Card>
        <CardHeader>
          <CardTitle>Crane Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div className="h-96 flex items-center justify-center">Loading calendar...</div>}>
            <ScheduleCalendar />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
