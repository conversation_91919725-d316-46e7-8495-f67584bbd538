-- Create lift_bookings table
CREATE TABLE IF NOT EXISTS lift_bookings (
  id SERIAL PRIMARY KEY,
  crane_id INTEGER NOT NULL REFERENCES cranes(id),
  project_id TEXT NOT NULL REFERENCES projects(id),
  booked_by_id INTEGER NOT NULL REFERENCES users(id),
  
  -- Lift scheduling details
  lift_date DATE NOT NULL,
  start_time VARCHAR(8) NOT NULL,
  number_of_lifts INTEGER NOT NULL DEFAULT 1,
  slot_duration_minutes INTEGER NOT NULL DEFAULT 30,
  
  -- Lift details
  lift_purpose VARCHAR(50) NOT NULL,
  item_description TEXT NOT NULL,
  pickup_location VARCHAR(100),
  dropoff_location VARCHAR(100) NOT NULL,
  estimated_weight DECIMAL(10,2),
  special_requirements TEXT,
  
  -- Booking management
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  confirmed_by_id INTEGER REFERENCES users(id),
  confirmed_at TIMESTAMP,
  
  -- Contact details
  contact_person VARCHAR(100) NOT NULL,
  contact_phone VARCHAR(20),
  
  -- Timestamps
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  deleted_at TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_lift_bookings_crane_date ON lift_bookings(crane_id, lift_date);
CREATE INDEX IF NOT EXISTS idx_lift_bookings_project ON lift_bookings(project_id);
CREATE INDEX IF NOT EXISTS idx_lift_bookings_status ON lift_bookings(status);
CREATE INDEX IF NOT EXISTS idx_lift_bookings_booked_by ON lift_bookings(booked_by_id);

-- Add some sample lift bookings
INSERT INTO lift_bookings (
  crane_id, project_id, booked_by_id, lift_date, start_time, number_of_lifts,
  lift_purpose, item_description, pickup_location, dropoff_location,
  estimated_weight, contact_person, contact_phone, status
) VALUES 
(
  (SELECT id FROM cranes LIMIT 1),
  (SELECT id FROM projects LIMIT 1),
  (SELECT id FROM users LIMIT 1),
  CURRENT_DATE,
  '09:00',
  2,
  'materials',
  'Steel beams for level 3',
  'Ground level storage',
  'Level 3 - East wing',
  2.5,
  'John Smith',
  '******-0123',
  'confirmed'
),
(
  (SELECT id FROM cranes LIMIT 1),
  (SELECT id FROM projects LIMIT 1),
  (SELECT id FROM users LIMIT 1),
  CURRENT_DATE,
  '11:00',
  1,
  'equipment',
  'Concrete mixer',
  'Loading bay',
  'Level 2 - Construction area',
  1.8,
  'Sarah Johnson',
  '******-0124',
  'pending'
),
(
  (SELECT id FROM cranes LIMIT 1),
  (SELECT id FROM projects LIMIT 1),
  (SELECT id FROM users LIMIT 1),
  CURRENT_DATE + INTERVAL '1 day',
  '08:30',
  3,
  'materials',
  'Prefab wall panels',
  'Staging area A',
  'Level 4 - North side',
  3.2,
  'Mike Wilson',
  '******-0125',
  'pending'
),
(
  (SELECT id FROM cranes LIMIT 1),
  (SELECT id FROM projects LIMIT 1),
  (SELECT id FROM users LIMIT 1),
  CURRENT_DATE + INTERVAL '1 day',
  '14:00',
  1,
  'personnel',
  'Maintenance crew lift',
  'Ground level',
  'Roof access',
  0.3,
  'Lisa Brown',
  '******-0126',
  'confirmed'
) ON CONFLICT DO NOTHING;
