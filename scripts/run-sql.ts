import { db } from '../lib/db';
import { sql } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';

async function runSQL() {
  try {
    console.log('Reading SQL file...');
    const sqlContent = fs.readFileSync(path.join(__dirname, 'create-lift-table.sql'), 'utf8');
    
    console.log('Executing SQL...');
    await db.execute(sql.raw(sqlContent));
    
    console.log('✅ SQL executed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error executing SQL:', error);
    process.exit(1);
  }
}

runSQL();
