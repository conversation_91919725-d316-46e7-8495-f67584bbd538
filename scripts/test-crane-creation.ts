#!/usr/bin/env tsx

/**
 * Test script for crane creation functionality
 * This script tests the crane creation API and validates the responses
 */

const API_BASE = 'http://localhost:3000';

interface CraneData {
  name: string;
  model?: string;
  type: string;
  capacity?: number;
  height?: number;
  reach?: number;
  teamId: number;
  status?: string;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
}

async function testCraneCreation() {
  console.log('🧪 Testing crane creation functionality...\n');

  // Test 1: Get available teams
  console.log('1. Fetching available teams...');
  try {
    const teamsResponse = await fetch(`${API_BASE}/api/teams`);
    const teamsData = await teamsResponse.json();
    console.log(`✅ Found ${teamsData.teams.length} teams`);
    
    if (teamsData.teams.length === 0) {
      console.log('❌ No teams available for testing');
      return;
    }
  } catch (error) {
    console.log('❌ Failed to fetch teams:', error);
    return;
  }

  // Test 2: Create a basic crane
  console.log('\n2. Creating a basic crane...');
  const basicCrane: CraneData = {
    name: 'Test Basic Crane',
    type: 'tower',
    teamId: 1
  };

  try {
    const response = await fetch(`${API_BASE}/api/cranes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(basicCrane)
    });

    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Created basic crane with ID: ${result.crane.id}`);
    } else {
      const error = await response.text();
      console.log(`❌ Failed to create basic crane: ${error}`);
    }
  } catch (error) {
    console.log('❌ Error creating basic crane:', error);
  }

  // Test 3: Create a detailed crane
  console.log('\n3. Creating a detailed crane...');
  const detailedCrane: CraneData = {
    name: 'Test Detailed Crane',
    model: 'Test Model XYZ',
    type: 'mobile',
    capacity: 50.5,
    height: 45.0,
    reach: 55.0,
    teamId: 1,
    status: 'maintenance',
    lastMaintenanceDate: '2025-01-01',
    nextMaintenanceDate: '2025-07-01'
  };

  try {
    const response = await fetch(`${API_BASE}/api/cranes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(detailedCrane)
    });

    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Created detailed crane with ID: ${result.crane.id}`);
      console.log(`   - Capacity: ${result.crane.capacity} tonnes`);
      console.log(`   - Height: ${result.crane.height} meters`);
      console.log(`   - Reach: ${result.crane.reach} meters`);
    } else {
      const error = await response.text();
      console.log(`❌ Failed to create detailed crane: ${error}`);
    }
  } catch (error) {
    console.log('❌ Error creating detailed crane:', error);
  }

  // Test 4: Test validation errors
  console.log('\n4. Testing validation errors...');
  
  const invalidCranes = [
    { name: '', type: 'tower', teamId: 1, expectedError: 'Name is required' },
    { name: 'Test', type: '', teamId: 1, expectedError: 'Type is required' },
    { name: 'Test', type: 'tower', teamId: 999, expectedError: 'Team not found' },
  ];

  for (const [index, invalidCrane] of invalidCranes.entries()) {
    try {
      const response = await fetch(`${API_BASE}/api/cranes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidCrane)
      });

      const error = await response.text();
      if (error === invalidCrane.expectedError) {
        console.log(`✅ Validation ${index + 1}: Correctly rejected with "${error}"`);
      } else {
        console.log(`❌ Validation ${index + 1}: Expected "${invalidCrane.expectedError}", got "${error}"`);
      }
    } catch (error) {
      console.log(`❌ Validation ${index + 1}: Unexpected error:`, error);
    }
  }

  // Test 5: Get all cranes
  console.log('\n5. Fetching all cranes...');
  try {
    const response = await fetch(`${API_BASE}/api/cranes`);
    const data = await response.json();
    console.log(`✅ Retrieved ${data.cranes.length} cranes`);
    
    data.cranes.forEach((crane: any, index: number) => {
      console.log(`   ${index + 1}. ${crane.name} (${crane.type}) - Team: ${crane.team.name}`);
    });
  } catch (error) {
    console.log('❌ Failed to fetch cranes:', error);
  }

  console.log('\n🎉 Crane creation testing completed!');
}

// Run the test
testCraneCreation().catch(console.error);
