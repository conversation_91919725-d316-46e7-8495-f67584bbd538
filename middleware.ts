import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

const isPublicRoute = createRouteMatcher([
  '/',
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/pricing',
  '/about',
  '/contact',
  '/terms',
  '/privacy',
  '/onboarding',
  '/debug-auth',
  '/test-auth',
]);

const isApiRoute = createRouteMatcher([
  '/api/organizations(.*)',
  '/api/teams(.*)',
  '/api/projects(.*)',
  '/api/cranes(.*)',
  '/api/bookings(.*)',
  '/api/lift-bookings(.*)',
  '/api/setup(.*)',
  '/api/users/onboarding',
]);

const isWebhookRoute = createRouteMatcher([
  '/api/webhook/clerk',
  '/api/webhook/stripe',
]);

export default clerkMiddleware((auth, req) => {
  const pathname = req.nextUrl.pathname;

  // Allow webhook routes to bypass authentication
  if (isWebhookRoute(req)) {
    return NextResponse.next();
  }

  // Allow public routes
  if (isPublicRoute(req)) {
    return NextResponse.next();
  }

  // For protected routes, use auth.protect()
  if (!isApiRoute(req)) {
    auth.protect();
  }

  // For API routes, use auth.protect() which will throw if not authenticated
  if (isApiRoute(req)) {
    try {
      auth.protect();
      return NextResponse.next();
    } catch (error) {
      return new NextResponse(
        JSON.stringify({ error: 'Unauthorized' }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  }

  return NextResponse.next();
});

export const config = {
  matcher: [
    '/((?!.+\\.[\\w]+$|_next).*)', // match all paths not ending in file extension or starting with _next
    '/', // match the root path
    '/(api|trpc)(.*)', // match API and tRPC routes
  ],
};
